{"name": "flarum-tsconfig", "version": "2.0.0", "description": "<PERSON><PERSON><PERSON>'s official Typescript config file", "main": "tsconfig.json", "repository": "https://github.com/flarum/flarum-tsconfig", "author": "Flarum Team", "license": "MIT", "dependencies": {"@types/jquery": "^3.5.5", "@types/mithril": "^2.0.7", "@types/throttle-debounce": "^2.1.0", "dayjs": "^1.10.4"}, "scripts": {"dev": "echo 'skipping..'", "build": "echo 'skipping..'", "analyze": "echo 'skipping..'", "format": "prettier --write .", "format-check": "prettier --check .", "clean-typings": "echo 'skipping..'", "build-typings": "echo 'skipping..'", "post-build-typings": "echo 'skipping..'", "check-typings": "echo 'skipping..'", "check-typings-coverage": "echo 'skipping..'"}}